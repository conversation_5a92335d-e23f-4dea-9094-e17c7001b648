"""
Reaction Pathway Module

Calculates reaction pathways using NEB (Nudged Elastic Band) method and
transition state optimization. Provides detailed reaction coordinate analysis.

Author: AI Chemistry Lab
License: MIT
"""

import numpy as np
import warnings
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from ase import Atoms
from ase.neb import NEB
from ase.optimize import BFGS, LBFGS
from ase.constraints import FixAtoms

try:
    from ase.calculators.emt import EMT
    EMT_AVAILABLE = True
except ImportError:
    EMT_AVAILABLE = False

from molecule_optimizer import MoleculeOptimizer


@dataclass
class PathwayResult:
    """Container for reaction pathway calculation results."""
    images: List[Atoms]
    energies: np.ndarray
    forces: List[np.ndarray]
    converged: bool
    n_iterations: int
    activation_energy: float
    reaction_energy: float
    transition_state_index: int
    transition_state: Optional[Atoms]
    reaction_coordinate: np.ndarray
    method: str
    calculation_time: Optional[float]


class ReactionPathwayCalculator:
    """
    Calculates reaction pathways using various methods:
    - NEB (Nudged Elastic Band)
    - CI-NEB (Climbing Image NEB)
    - String method
    - Transition state optimization
    """
    
    def __init__(self):
        """Initialize the pathway calculator."""
        self.optimizer = MoleculeOptimizer()
        
        # NEB parameters
        self.default_n_images = 8
        self.max_force = 0.05  # eV/Å
        self.max_iterations = 200
        self.spring_constant = 1.0  # eV/Å²
        
        # Transition state search parameters
        self.ts_max_force = 0.01  # eV/Å
        self.ts_max_iterations = 100
    
    def calculate_pathway(self, reactants: List[Atoms], 
                         products: List[str],
                         n_images: int = 8,
                         method: str = "NEB",
                         calculator: Optional[str] = None,
                         max_force: float = 0.05) -> PathwayResult:
        """
        Calculate reaction pathway between reactants and products.
        
        Args:
            reactants: List of reactant Atoms objects
            products: List of product SMILES strings
            n_images: Number of images in the pathway
            method: Pathway method ('NEB', 'CI-NEB', 'String')
            calculator: Calculator to use
            max_force: Force convergence criterion
            
        Returns:
            PathwayResult object
        """
        import time
        start_time = time.time()
        
        print(f"🛤️ Calculating reaction pathway using {method}")
        print(f"📊 Images: {n_images}, Force tolerance: {max_force} eV/Å")
        
        # Convert products to Atoms objects
        product_atoms = self._prepare_products(products)

        # Handle case where no valid products were found
        if not product_atoms:
            print("⚠️ No valid products found, using reactants as products (no reaction)")
            product_atoms = reactants.copy()

        # Create initial and final states
        initial_state = self._combine_molecules(reactants)
        final_state = self._combine_molecules(product_atoms)

        # Generate initial pathway with error handling
        try:
            images = self._generate_initial_pathway(
                initial_state, final_state, n_images
            )
        except Exception as e:
            print(f"⚠️ Pathway generation failed: {e}")
            # Fallback: create pathway with only initial state
            images = [initial_state.copy() for _ in range(n_images)]
        
        # Set up calculator for all images
        calc = self.optimizer._setup_calculator(initial_state, calculator)
        for image in images:
            image.set_calculator(calc)
        
        # Run pathway calculation
        if method.upper() == "NEB":
            result = self._run_neb(images, max_force)
        elif method.upper() == "CI-NEB":
            result = self._run_ci_neb(images, max_force)
        elif method.upper() == "STRING":
            result = self._run_string_method(images, max_force)
        else:
            raise ValueError(f"Unknown pathway method: {method}")
        
        # Analyze results
        energies = np.array([img.get_potential_energy() for img in result['images']])
        forces = [img.get_forces() for img in result['images']]
        
        # Find transition state
        ts_index = np.argmax(energies)
        activation_energy = energies[ts_index] - energies[0]
        reaction_energy = energies[-1] - energies[0]
        
        # Generate reaction coordinate
        reaction_coordinate = self._calculate_reaction_coordinate(result['images'])
        
        calculation_time = time.time() - start_time
        
        return PathwayResult(
            images=result['images'],
            energies=energies,
            forces=forces,
            converged=result['converged'],
            n_iterations=result['n_iterations'],
            activation_energy=activation_energy,
            reaction_energy=reaction_energy,
            transition_state_index=ts_index,
            transition_state=result['images'][ts_index].copy(),
            reaction_coordinate=reaction_coordinate,
            method=method,
            calculation_time=calculation_time
        )
    
    def _prepare_products(self, products: List[str]) -> List[Atoms]:
        """Convert product SMILES to optimized Atoms objects with error handling."""
        from input_handler import InputHandler

        handler = InputHandler()
        product_atoms = []

        for product_smiles in products:
            try:
                # Validate SMILES first
                if not self._is_valid_smiles(product_smiles):
                    print(f"⚠️ Invalid SMILES detected: {product_smiles}")
                    continue

                atoms = handler.parse_molecule(product_smiles)

                # Quick optimization with error handling
                try:
                    opt_result = self.optimizer.optimize_geometry(atoms)
                    if opt_result.converged:
                        product_atoms.append(opt_result.atoms)
                    else:
                        print(f"⚠️ Product optimization failed: {product_smiles}")
                        product_atoms.append(atoms)
                except Exception as opt_error:
                    print(f"⚠️ Optimization error for {product_smiles}: {opt_error}")
                    product_atoms.append(atoms)  # Use unoptimized

            except Exception as e:
                print(f"⚠️ Could not process product {product_smiles}: {e}")
                continue

        return product_atoms

    def _is_valid_smiles(self, smiles: str) -> bool:
        """Check if a SMILES string is valid."""
        try:
            # Try importing RDKit for validation
            try:
                from rdkit import Chem
                mol = Chem.MolFromSmiles(smiles)
                return mol is not None
            except ImportError:
                # Fallback: basic validation
                # Check for obvious invalid patterns
                invalid_patterns = [
                    '.H.', '.C.', '.O.',  # Invalid dot-separated single atoms
                    '..', '...', # Multiple dots
                ]
                return not any(pattern in smiles for pattern in invalid_patterns)
        except Exception:
            return False
    
    def _combine_molecules(self, molecules: List[Atoms]) -> Atoms:
        """Combine multiple molecules into a single Atoms object."""
        if len(molecules) == 1:
            return molecules[0].copy()
        
        # Combine all molecules
        combined = molecules[0].copy()
        
        for mol in molecules[1:]:
            # Translate molecule to avoid overlap
            mol_copy = mol.copy()
            mol_copy.translate([len(combined) * 3.0, 0, 0])  # Simple separation
            combined.extend(mol_copy)
        
        return combined
    
    def _generate_initial_pathway(self, initial: Atoms, final: Atoms,
                                n_images: int) -> List[Atoms]:
        """Generate initial pathway by linear interpolation with atom count handling."""

        # Handle atom count mismatch by using a more flexible approach
        if len(initial) != len(final):
            print(f"⚠️ Atom count mismatch: initial={len(initial)}, final={len(final)}")
            print("🔄 Using initial state for all images (no reaction pathway)")

            # Create pathway using only the initial state
            # This represents a "no reaction" scenario
            images = []
            for i in range(n_images):
                image = initial.copy()
                images.append(image)
            return images

        images = []

        # Create intermediate images by linear interpolation
        for i in range(n_images):
            fraction = i / (n_images - 1)

            # Interpolate positions
            pos_initial = initial.get_positions()
            pos_final = final.get_positions()
            pos_interpolated = (1 - fraction) * pos_initial + fraction * pos_final

            # Create new image
            image = initial.copy()
            image.set_positions(pos_interpolated)
            images.append(image)

        return images
    
    def _run_neb(self, images: List[Atoms], max_force: float) -> Dict[str, Any]:
        """Run standard NEB calculation."""
        
        # Create NEB object
        neb = NEB(images, k=self.spring_constant)
        
        # Set up optimizer
        optimizer = BFGS(neb, maxstep=0.2)
        
        try:
            # Run optimization
            optimizer.run(fmax=max_force, steps=self.max_iterations)
            converged = optimizer.converged()
            n_iterations = optimizer.get_number_of_steps()
        except Exception as e:
            warnings.warn(f"NEB optimization failed: {e}")
            converged = False
            n_iterations = self.max_iterations
        
        return {
            'images': images,
            'converged': converged,
            'n_iterations': n_iterations
        }
    
    def _run_ci_neb(self, images: List[Atoms], max_force: float) -> Dict[str, Any]:
        """Run Climbing Image NEB calculation."""
        
        # Create CI-NEB object
        neb = NEB(images, k=self.spring_constant, climb=True)
        
        # Set up optimizer
        optimizer = BFGS(neb, maxstep=0.2)
        
        try:
            # Run optimization
            optimizer.run(fmax=max_force, steps=self.max_iterations)
            converged = optimizer.converged()
            n_iterations = optimizer.get_number_of_steps()
        except Exception as e:
            warnings.warn(f"CI-NEB optimization failed: {e}")
            converged = False
            n_iterations = self.max_iterations
        
        return {
            'images': images,
            'converged': converged,
            'n_iterations': n_iterations
        }
    
    def _run_string_method(self, images: List[Atoms], max_force: float) -> Dict[str, Any]:
        """Run string method calculation (simplified implementation)."""
        
        # For now, use standard NEB as placeholder
        # A full string method implementation would require:
        # - Reparametrization steps
        # - Arc-length parametrization
        # - Iterative optimization
        
        warnings.warn("String method not fully implemented, using NEB")
        return self._run_neb(images, max_force)
    
    def _calculate_reaction_coordinate(self, images: List[Atoms]) -> np.ndarray:
        """Calculate reaction coordinate as cumulative distance along pathway."""
        
        coordinates = [0.0]  # Start at 0
        
        for i in range(1, len(images)):
            # Calculate distance between consecutive images
            pos1 = images[i-1].get_positions()
            pos2 = images[i].get_positions()
            
            # Root mean square distance
            distance = np.sqrt(np.mean((pos2 - pos1)**2))
            coordinates.append(coordinates[-1] + distance)
        
        return np.array(coordinates)
    
    def optimize_transition_state(self, ts_guess: Atoms,
                                calculator: Optional[str] = None) -> Dict[str, Any]:
        """
        Optimize transition state structure.
        
        Args:
            ts_guess: Initial guess for transition state
            calculator: Calculator to use
            
        Returns:
            Dictionary with optimization results
        """
        
        # Set up calculator
        calc = self.optimizer._setup_calculator(ts_guess, calculator)
        ts_guess.set_calculator(calc)
        
        try:
            # For a proper TS optimization, you would use:
            # - Dimer method
            # - Eigenvector following
            # - Quadratic synchronous transit (QST)
            
            # Simplified: just optimize with constraints
            from ase.optimize import BFGS
            
            opt = BFGS(ts_guess, maxstep=0.1)
            opt.run(fmax=self.ts_max_force, steps=self.ts_max_iterations)
            
            converged = opt.converged()
            energy = ts_guess.get_potential_energy()
            forces = ts_guess.get_forces()
            
            return {
                'atoms': ts_guess.copy(),
                'energy': energy,
                'forces': forces,
                'converged': converged,
                'method': 'constrained_optimization'
            }
            
        except Exception as e:
            warnings.warn(f"TS optimization failed: {e}")
            return {
                'atoms': ts_guess.copy(),
                'energy': float('inf'),
                'forces': np.zeros((len(ts_guess), 3)),
                'converged': False,
                'method': 'failed'
            }
    
    def calculate_irc(self, transition_state: Atoms,
                     direction: str = "both",
                     step_size: float = 0.1,
                     n_steps: int = 20) -> Dict[str, Any]:
        """
        Calculate Intrinsic Reaction Coordinate (IRC).
        
        Args:
            transition_state: Transition state structure
            direction: Direction to follow ('forward', 'reverse', 'both')
            step_size: Step size for IRC
            n_steps: Number of IRC steps
            
        Returns:
            Dictionary with IRC results
        """
        
        # This is a simplified IRC implementation
        # A full implementation would require:
        # - Hessian calculation
        # - Mass-weighted coordinates
        # - Predictor-corrector algorithm
        
        warnings.warn("IRC calculation not fully implemented")
        
        return {
            'forward_path': [],
            'reverse_path': [],
            'energies': [],
            'method': 'simplified'
        }
    
    def analyze_pathway_properties(self, pathway: PathwayResult) -> Dict[str, Any]:
        """Analyze properties of the calculated pathway."""
        
        analysis = {
            'n_images': len(pathway.images),
            'activation_energy': pathway.activation_energy,
            'reaction_energy': pathway.reaction_energy,
            'ts_position': pathway.transition_state_index / (len(pathway.images) - 1),
            'pathway_length': pathway.reaction_coordinate[-1],
            'energy_profile': pathway.energies.tolist(),
            'max_force': max(np.max(np.linalg.norm(f, axis=1)) for f in pathway.forces),
            'converged': pathway.converged
        }
        
        # Classify reaction type based on energy profile
        if pathway.reaction_energy > 0:
            analysis['reaction_type'] = 'endothermic'
        else:
            analysis['reaction_type'] = 'exothermic'
        
        if pathway.activation_energy < 0.5:
            analysis['barrier_height'] = 'low'
        elif pathway.activation_energy < 1.5:
            analysis['barrier_height'] = 'moderate'
        else:
            analysis['barrier_height'] = 'high'
        
        # Check for intermediates (local minima)
        energies = pathway.energies
        intermediates = []
        for i in range(1, len(energies) - 1):
            if energies[i] < energies[i-1] and energies[i] < energies[i+1]:
                intermediates.append(i)
        
        analysis['intermediates'] = intermediates
        analysis['n_intermediates'] = len(intermediates)
        
        return analysis


if __name__ == "__main__":
    # Example usage
    from input_handler import InputHandler
    
    handler = InputHandler()
    calculator = ReactionPathwayCalculator()
    
    try:
        # Simple test: H2 dissociation
        h2_coords = "H 0.0 0.0 0.0\nH 0.74 0.0 0.0"
        h2_separated = "H 0.0 0.0 0.0\nH 3.0 0.0 0.0"
        
        initial = handler.parse_molecule(h2_coords)
        final = handler.parse_molecule(h2_separated)
        
        # Create pathway
        pathway = calculator.calculate_pathway(
            [initial], ["[H]"], n_images=5, method="NEB"
        )
        
        print(f"Pathway converged: {pathway.converged}")
        print(f"Activation energy: {pathway.activation_energy:.3f} eV")
        print(f"Reaction energy: {pathway.reaction_energy:.3f} eV")
        print(f"TS at image {pathway.transition_state_index}")
        
        # Analyze pathway
        analysis = calculator.analyze_pathway_properties(pathway)
        print(f"Analysis: {analysis}")
        
    except Exception as e:
        print(f"Test failed: {e}")
